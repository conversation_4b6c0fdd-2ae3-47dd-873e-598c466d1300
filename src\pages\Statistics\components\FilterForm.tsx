import type { IStatisticsQuery } from '@/types/statistics';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Card, Col, Form, Input, Row, Select, Space } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';

const { Option } = Select;

interface FilterFormProps {
  loading?: boolean;
  onFilter: (values: IStatisticsQuery) => void;
  onReset: () => void;
}

/**
 * 数据筛选表单组件
 */
const FilterForm: React.FC<FilterFormProps> = ({
  loading = false,
  onFilter,
  onReset,
}) => {
  const [form] = Form.useForm();

  // 处理筛选
  const handleFilter = (values: any) => {
    const filterParams: IStatisticsQuery = {
      month: values.month ? dayjs(values.month).format('YYYY-MM') : undefined,
      subject: values.subject,
      department: values.department,
    };
    onFilter(filterParams);
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  // 生成月份选项（最近12个月）
  const getMonthOptions = () => {
    const months = [];
    const current = dayjs();

    for (let i = 0; i < 12; i++) {
      const month = current.subtract(i, 'month');
      months.push({
        value: month.format('YYYY-MM'),
        label: month.format('YYYY年MM月'),
      });
    }

    return months;
  };

  // 初始化默认值
  useEffect(() => {
    form.setFieldsValue({
      month: dayjs().format('YYYY-MM'), // 默认当前月份
    });
  }, [form]);

  return (
    <Card style={{ marginBottom: 16 }}>
      <Form
        form={form}
        layout="inline"
        onFinish={handleFilter}
        style={{ width: '100%' }}
      >
        <Row gutter={16} style={{ width: '100%' }}>
          <Col span={6}>
            <Form.Item name="month" label="月份">
              <Select
                placeholder="请选择月份"
                allowClear
                style={{ width: '100%' }}
              >
                {getMonthOptions().map((month) => (
                  <Option key={month.value} value={month.value}>
                    {month.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item name="subject" label="科目">
              <Input
                placeholder="请输入科目"
                allowClear
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item name="department" label="部门">
              <Input
                placeholder="请输入部门"
                allowClear
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                >
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default FilterForm;
