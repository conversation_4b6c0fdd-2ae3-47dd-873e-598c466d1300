import type { IStatisticsQuery } from '@/types/statistics';
import { getUserInfo } from '@/utils/auth';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import FilterForm from './components/FilterForm';
import SchoolOverview from './components/SchoolOverview';
import TeacherDetailModal from './components/TeacherDetailModal';
import TeacherRanking from './components/TeacherRanking';
import './index.less';

/**
 * 统计分析页面
 */
const Statistics: React.FC = () => {
  const {
    // 状态
    loading,
    modalLoading,
    schoolStatistics,
    teacherRanking,
    teacherTotal,
    teacherDetail,
    scoreDistribution,
    keywordData,
    filters,

    // 方法
    fetchSchoolStatistics,
    fetchTeacherRanking,
    fetchTeacherDetail,
    updateFilters,
    clearTeacherDetail,
  } = useModel('statistics');

  // 教师详情模态框状态
  const [modalVisible, setModalVisible] = useState(false);

  // 获取当前用户学校信息
  const getCurrentUserSchoolCode = () => {
    const userInfo = getUserInfo();
    return userInfo?.enterprise?.code || '';
  };

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      const schoolCode = getCurrentUserSchoolCode();
      const currentMonth = dayjs().format('YYYY-MM'); // 获取当前月份
      const initialParams: IStatisticsQuery = {
        sso_school_code: schoolCode,
        month: currentMonth, // 添加默认月份参数
      };

      // 获取初始统计数据
      await Promise.all([
        fetchSchoolStatistics(initialParams),
        fetchTeacherRanking(initialParams),
      ]);
    };

    initData();
  }, [fetchSchoolStatistics, fetchTeacherRanking]);

  // 处理筛选
  const handleFilter = async (filterParams: IStatisticsQuery) => {
    const schoolCode = getCurrentUserSchoolCode();
    const params = {
      ...filterParams,
      sso_school_code: schoolCode,
    };
    await updateFilters(params);
  };

  // 处理重置筛选
  const handleReset = async () => {
    const schoolCode = getCurrentUserSchoolCode();
    const currentMonth = dayjs().format('YYYY-MM'); // 重置时也使用当前月份
    const params: IStatisticsQuery = {
      sso_school_code: schoolCode,
      month: currentMonth, // 添加默认月份参数
    };
    await updateFilters(params);
  };

  // 处理教师详情点击
  const handleTeacherClick = async (teacherId: string) => {
    setModalVisible(true);
    await fetchTeacherDetail(teacherId, filters);
  };

  // 关闭教师详情模态框
  const handleModalClose = () => {
    setModalVisible(false);
    clearTeacherDetail();
  };

  return (
    <div className="statistics-page">
      {/* 筛选表单 */}
      <FilterForm
        loading={loading}
        onFilter={handleFilter}
        onReset={handleReset}
      />

      {/* 学校整体统计 */}
      <SchoolOverview data={schoolStatistics} loading={loading} />

      {/* 教师排行榜 */}
      <TeacherRanking
        data={teacherRanking}
        total={teacherTotal}
        loading={loading}
        onTeacherClick={handleTeacherClick}
      />

      {/* 教师详情模态框 */}
      <TeacherDetailModal
        visible={modalVisible}
        loading={modalLoading}
        teacherDetail={teacherDetail}
        scoreDistribution={scoreDistribution}
        keywordData={keywordData}
        onClose={handleModalClose}
      />
    </div>
  );
};

export default Statistics;
