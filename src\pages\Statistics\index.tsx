import type { IStatisticsQuery } from '@/types/statistics';
import { getUserInfo } from '@/utils/auth';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import FilterForm from './components/FilterForm';
import IncompleteStudents from './components/IncompleteStudents';
import SchoolOverview from './components/SchoolOverview';
import TeacherDetailModal from './components/TeacherDetailModal';
import TeacherRanking from './components/TeacherRanking';
import './index.less';

/**
 * 统计分析页面
 */
const Statistics: React.FC = () => {
  const {
    // 状态
    loading,
    modalLoading,
    schoolStatistics,
    teacherRanking,
    teacherTotal,
    teacherDetail,
    scoreDistribution,
    keywordData,
    filters,
    incompleteStudents,
    incompleteStudentsLoading,

    // 方法
    fetchSchoolStatistics,
    fetchTeacherRanking,
    fetchTeacherDetail,
    fetchIncompleteStudents,
    clearTeacherDetail,
    clearIncompleteStudents,
  } = useModel('statistics');

  // 教师详情模态框状态
  const [modalVisible, setModalVisible] = useState(false);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 未填写学生显示状态
  const [showIncompleteStudents, setShowIncompleteStudents] = useState(false);

  // 未填写学生分页状态
  const [incompleteStudentsPage, setIncompleteStudentsPage] = useState(1);
  const [incompleteStudentsPageSize, setIncompleteStudentsPageSize] = useState(20);
  const [incompleteStudentsFilters, setIncompleteStudentsFilters] = useState<any>({});

  // 获取当前用户学校信息
  const getCurrentUserSchoolCode = () => {
    const userInfo = getUserInfo();
    return userInfo?.enterprise?.code || '';
  };

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      const schoolCode = getCurrentUserSchoolCode();
      const currentMonth = dayjs().format('YYYY-MM'); // 获取当前月份

      // 学校统计参数（不需要分页）
      const schoolParams: IStatisticsQuery = {
        sso_school_code: schoolCode,
        month: currentMonth,
      };

      // 教师排名参数（需要分页）
      const teacherParams: IStatisticsQuery = {
        sso_school_code: schoolCode,
        month: currentMonth,
        page: 1,
        limit: pageSize,
      };

      // 获取初始统计数据
      await Promise.all([
        fetchSchoolStatistics(schoolParams),
        fetchTeacherRanking(teacherParams),
      ]);
    };

    initData();
  }, [fetchSchoolStatistics, fetchTeacherRanking, pageSize]);

  // 处理筛选
  const handleFilter = async (filterParams: IStatisticsQuery) => {
    const schoolCode = getCurrentUserSchoolCode();

    // 学校统计参数（不需要分页）
    const schoolParams = {
      ...filterParams,
      sso_school_code: schoolCode,
    };

    // 教师排名参数（需要分页）
    const teacherParams = {
      ...filterParams,
      sso_school_code: schoolCode,
      page: 1, // 重置到第一页
      limit: pageSize,
    };

    setCurrentPage(1);

    // 分别更新学校统计和教师排名
    await Promise.all([
      fetchSchoolStatistics(schoolParams),
      fetchTeacherRanking(teacherParams),
    ]);

    // 清空未填写学生数据
    clearIncompleteStudents();
  };

  // 处理重置筛选
  const handleReset = async () => {
    const schoolCode = getCurrentUserSchoolCode();
    const currentMonth = dayjs().format('YYYY-MM'); // 重置时也使用当前月份

    // 学校统计参数（不需要分页）
    const schoolParams: IStatisticsQuery = {
      sso_school_code: schoolCode,
      month: currentMonth,
    };

    // 教师排名参数（需要分页）
    const teacherParams: IStatisticsQuery = {
      sso_school_code: schoolCode,
      month: currentMonth,
      page: 1,
      limit: pageSize,
    };

    setCurrentPage(1);

    // 分别重置学校统计和教师排名
    await Promise.all([
      fetchSchoolStatistics(schoolParams),
      fetchTeacherRanking(teacherParams),
    ]);

    // 清空未填写学生数据
    clearIncompleteStudents();
    setShowIncompleteStudents(false);
  };

  // 处理教师详情点击
  const handleTeacherClick = async (teacherId: string) => {
    setModalVisible(true);
    await fetchTeacherDetail(teacherId, filters);
  };

  // 关闭教师详情模态框
  const handleModalClose = () => {
    setModalVisible(false);
    clearTeacherDetail();
  };

  // 处理分页变化
  const handlePageChange = async (page: number, size: number) => {
    const schoolCode = getCurrentUserSchoolCode();
    const params = {
      ...filters,
      sso_school_code: schoolCode,
      page,
      limit: size,
    };
    setCurrentPage(page);
    setPageSize(size);
    await fetchTeacherRanking(params);
  };

  // 处理排序变化
  const handleSortChange = async (sortBy: string, sortOrder: 'ASC' | 'DESC') => {
    const schoolCode = getCurrentUserSchoolCode();
    const params = {
      ...filters,
      sso_school_code: schoolCode,
      page: currentPage,
      limit: pageSize,
      sort_by: sortBy as any,
      sort_order: sortOrder,
    };
    await fetchTeacherRanking(params);
  };

  // 获取未填写学生数据
  const handleFetchIncompleteStudents = async (
    page: number = incompleteStudentsPage,
    pageSize: number = incompleteStudentsPageSize,
    additionalFilters: any = {}
  ) => {
    const schoolCode = getCurrentUserSchoolCode();
    const currentMonth = dayjs().format('YYYY-MM');
    const params = {
      sso_school_code: schoolCode,
      month: filters.month || currentMonth,
      page,
      pageSize,
      ...incompleteStudentsFilters,
      ...additionalFilters,
    };
    await fetchIncompleteStudents(params);
    setShowIncompleteStudents(true);
  };

  // 刷新未填写学生数据
  const handleRefreshIncompleteStudents = async () => {
    if (showIncompleteStudents) {
      await handleFetchIncompleteStudents();
    }
  };

  // 处理未填写学生分页变化
  const handleIncompleteStudentsPageChange = async (page: number, pageSize: number) => {
    setIncompleteStudentsPage(page);
    setIncompleteStudentsPageSize(pageSize);
    await handleFetchIncompleteStudents(page, pageSize);
  };

  // 处理未填写学生筛选变化
  const handleIncompleteStudentsFilterChange = async (newFilters: any) => {
    setIncompleteStudentsFilters(newFilters);
    setIncompleteStudentsPage(1); // 重置到第一页
    await handleFetchIncompleteStudents(1, incompleteStudentsPageSize, newFilters);
  };

  return (
    <div className="statistics-page">
      {/* 筛选表单 */}
      <FilterForm
        loading={loading}
        onFilter={handleFilter}
        onReset={handleReset}
      />

      {/* 学校整体统计 */}
      <SchoolOverview
        data={schoolStatistics}
        loading={loading}
        onShowIncompleteStudents={handleFetchIncompleteStudents}
      />

      {/* 未填写学生统计 */}
      {showIncompleteStudents && (
        <IncompleteStudents
          data={incompleteStudents}
          loading={incompleteStudentsLoading}
          onRefresh={handleRefreshIncompleteStudents}
          onPageChange={handleIncompleteStudentsPageChange}
          onFilterChange={handleIncompleteStudentsFilterChange}
        />
      )}

      {/* 教师排行榜 */}
      <TeacherRanking
        data={teacherRanking}
        total={teacherTotal}
        loading={loading}
        currentPage={currentPage}
        pageSize={pageSize}
        onTeacherClick={handleTeacherClick}
        onPageChange={handlePageChange}
        onSortChange={handleSortChange}
      />

      {/* 教师详情模态框 */}
      <TeacherDetailModal
        visible={modalVisible}
        loading={modalLoading}
        teacherDetail={teacherDetail}
        scoreDistribution={scoreDistribution}
        keywordData={keywordData}
        onClose={handleModalClose}
      />
    </div>
  );
};

export default Statistics;
