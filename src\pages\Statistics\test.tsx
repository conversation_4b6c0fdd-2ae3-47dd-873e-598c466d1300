import type {
  IIncompleteStudentsResponse,
  ISchoolStatistics,
  ITeacherRanking,
} from '@/types/statistics';
import { Card, Divider } from 'antd';
import React from 'react';
import IncompleteStudents from './components/IncompleteStudents';
import SchoolOverview from './components/SchoolOverview';
import TeacherRanking from './components/TeacherRanking';

/**
 * 统计分析功能测试页面
 * 用于测试新增的功能和API更新
 */
const StatisticsTest: React.FC = () => {
  // 模拟更新后的学校统计数据
  const mockSchoolData: ISchoolStatistics = {
    sso_school_code: 'school_001',
    sso_school_name: '示例小学',
    month: '2024-01',
    total_responses: 150,
    completed_responses: 135,
    total_students: 160, // 新增：学生总数
    completion_rate: 84.4, // 基于学生总数计算的完成率
    school_average_score: 88.5,
    teacher_average_score: 87.2,
    total_teachers_evaluated: 25,
    incomplete_students_summary: {
      // 新增：未填写学生统计
      total_incomplete: 25,
      by_grade: [
        {
          grade_code: 'grade_1',
          grade_name: '一年级',
          incomplete_count: 10,
          total_students: 60,
          completion_rate: 83.3,
        },
        {
          grade_code: 'grade_2',
          grade_name: '二年级',
          incomplete_count: 8,
          total_students: 50,
          completion_rate: 84.0,
        },
        {
          grade_code: 'grade_3',
          grade_name: '三年级',
          incomplete_count: 7,
          total_students: 50,
          completion_rate: 86.0,
        },
      ],
      by_class: [
        {
          grade_code: 'grade_1',
          grade_name: '一年级',
          class_code: 'class_1_1',
          class_name: '一班',
          incomplete_count: 5,
          total_students: 30,
          completion_rate: 83.3,
          students: [
            {
              sso_student_code: 'stu_001',
              sso_student_name: '张小明',
              grade_code: 'grade_1',
              grade_name: '一年级',
              class_code: 'class_1_1',
              class_name: '一班',
            },
            {
              sso_student_code: 'stu_002',
              sso_student_name: '李小红',
              grade_code: 'grade_1',
              grade_name: '一年级',
              class_code: 'class_1_1',
              class_name: '一班',
            },
          ],
        },
        {
          grade_code: 'grade_1',
          grade_name: '一年级',
          class_code: 'class_1_2',
          class_name: '二班',
          incomplete_count: 5,
          total_students: 30,
          completion_rate: 83.3,
          students: [
            {
              sso_student_code: 'stu_003',
              sso_student_name: '王小华',
              grade_code: 'grade_1',
              grade_name: '一年级',
              class_code: 'class_1_2',
              class_name: '二班',
            },
          ],
        },
      ],
    },
  };

  // 模拟未填写学生数据
  const mockIncompleteStudentsData: IIncompleteStudentsResponse = {
    summary: {
      total_incomplete: 25,
      by_grade: mockSchoolData.incomplete_students_summary!.by_grade,
      by_class: mockSchoolData.incomplete_students_summary!.by_class,
    },
    pagination: {
      page: 1,
      pageSize: 20,
      total: 3,
      totalPages: 1,
    },
    classes: mockSchoolData.incomplete_students_summary!.by_class,
  };

  // 模拟教师排名数据
  const mockTeacherRankingData: ITeacherRanking[] = [
    {
      sso_teacher_id: 'teacher_001',
      sso_teacher_name: '张老师',
      sso_teacher_subject: '数学',
      sso_teacher_department: '小学部',
      average_score: 95.5,
      evaluation_count: 30,
      recommendation_rate: 96.7,
      rank: 1,
    },
    {
      sso_teacher_id: 'teacher_002',
      sso_teacher_name: '李老师',
      sso_teacher_subject: '语文',
      sso_teacher_department: '小学部',
      average_score: 94.2,
      evaluation_count: 28,
      recommendation_rate: 95.2,
      rank: 2,
    },
    {
      sso_teacher_id: 'teacher_003',
      sso_teacher_name: '王老师',
      sso_teacher_subject: '英语',
      sso_teacher_department: '小学部',
      average_score: 93.8,
      evaluation_count: 25,
      recommendation_rate: 94.8,
      rank: 3,
    },
  ];

  const handleTeacherClick = (teacherId: string) => {
    console.log('点击教师:', teacherId);
  };

  const handlePageChange = (page: number, pageSize: number) => {
    console.log('分页变化:', page, pageSize);
  };

  const handleSortChange = (sortBy: string, sortOrder: 'ASC' | 'DESC') => {
    console.log('排序变化:', sortBy, sortOrder);
  };

  const handleShowIncompleteStudents = () => {
    console.log('显示未填写学生统计');
  };

  const handleRefreshIncompleteStudents = () => {
    console.log('刷新未填写学生数据');
  };

  const handleIncompleteStudentsPageChange = (page: number, pageSize: number) => {
    console.log('未填写学生分页变化:', page, pageSize);
  };

  const handleIncompleteStudentsFilterChange = (filters: any) => {
    console.log('未填写学生筛选变化:', filters);
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="统计分析功能测试" style={{ marginBottom: 16 }}>
        <p>此页面用于测试统计分析模块的新功能，包括：</p>
        <ul>
          <li>基于学生总数的完成率计算</li>
          <li>未填写学生统计功能</li>
          <li>教师排名的服务端分页和排序</li>
          <li>新的API接口支持</li>
        </ul>
      </Card>

      <Divider>学校整体统计（包含未填写学生入口）</Divider>
      <SchoolOverview
        data={mockSchoolData}
        loading={false}
        onShowIncompleteStudents={handleShowIncompleteStudents}
      />

      <Divider>未填写学生统计</Divider>
      <IncompleteStudents
        data={mockIncompleteStudentsData}
        loading={false}
        onRefresh={handleRefreshIncompleteStudents}
        onPageChange={handleIncompleteStudentsPageChange}
        onFilterChange={handleIncompleteStudentsFilterChange}
      />

      <Divider>教师排名（支持服务端分页和排序）</Divider>
      <TeacherRanking
        data={mockTeacherRankingData}
        total={100}
        loading={false}
        currentPage={1}
        pageSize={20}
        onTeacherClick={handleTeacherClick}
        onPageChange={handlePageChange}
        onSortChange={handleSortChange}
      />
    </div>
  );
};

export default StatisticsTest;
