import {
  createQuestionnaire,
  deleteQuestionnaire,
  getQuestionnaireCourses,
  getQuestionnaireDetail,
  getQuestionnaireList,
  getSchoolCourses,
  updateQuestionnaire,
  updateQuestionnaireStatus,
} from '@/services';
import { handleApiResponse, handleException } from '@/utils/errorHandler';
import { useCallback, useState } from 'react';

/**
 * 问卷管理数据模型
 */
export default function useQuestionnaireModel() {
  const [questionnaireList, setQuestionnaireList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [questionnaireDetail, setQuestionnaireDetail] =
    useState<API.IQuestionnaireDetail | null>(null);
  const [detailLoading, setDetailLoading] = useState(false);

  // 课程相关状态
  const [schoolCourses, setSchoolCourses] = useState<API.ICourseInfo[]>([]);
  const [questionnaireCourses, setQuestionnaireCourses] = useState<
    API.IQuestionnaireCourse[]
  >([]);
  const [coursesLoading, setCoursesLoading] = useState(false);

  // 获取问卷列表
  const fetchQuestionnaireList = useCallback(
    async (params?: API.IQuestionnaireQuery) => {
      setLoading(true);
      try {
        const response = await getQuestionnaireList(params);
        const result = handleApiResponse(response);

        if (result.success) {
          setQuestionnaireList(result.data?.list || []);
          setTotal(result.data?.total || 0);
          return result.data;
        } else {
          setQuestionnaireList([]);
          setTotal(0);
          return null;
        }
      } catch (error) {
        handleException(error, '获取问卷列表失败');
        setQuestionnaireList([]);
        setTotal(0);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 创建问卷
  const createQuestionnaireAction = useCallback(
    async (params: API.ICreateQuestionnaireParams) => {
      const response = await createQuestionnaire(params);
      const result = handleApiResponse(response, '创建问卷成功');

      return result;
    },
    [],
  );

  // 更新问卷
  const updateQuestionnaireAction = useCallback(
    async (id: number, params: API.ICreateQuestionnaireParams) => {
      const response = await updateQuestionnaire(id, params);
      const result = handleApiResponse(response, '更新问卷成功');

      return result;
    },
    [],
  );

  // 更新问卷状态
  const updateQuestionnaireStatusAction = useCallback(
    async (id: number, params: API.IUpdateQuestionnaireStatusParams) => {
      try {
        const response = await updateQuestionnaireStatus(id, params);
        const result = handleApiResponse(response, '更新问卷状态成功');

        if (result.success) {
          // 刷新列表
          await fetchQuestionnaireList();
          return result.data;
        } else {
          return null;
        }
      } catch (error) {
        handleException(error, '更新问卷状态失败');
        return null;
      }
    },
    [fetchQuestionnaireList],
  );

  // 删除问卷
  const deleteQuestionnaireAction = useCallback(
    async (id: number) => {
      try {
        const response = await deleteQuestionnaire(id);
        const result = handleApiResponse(response, '删除问卷成功');

        if (result.success) {
          // 刷新列表
          await fetchQuestionnaireList();
          return true;
        } else {
          return false;
        }
      } catch (error) {
        handleException(error, '删除问卷失败');
        return false;
      }
    },
    [fetchQuestionnaireList],
  );

  // 获取问卷详情
  const fetchQuestionnaireDetail = useCallback(async (id: number) => {
    setDetailLoading(true);
    try {
      const response = await getQuestionnaireDetail(id);
      const result = handleApiResponse(response);

      if (result.success) {
        setQuestionnaireDetail(result.data || null);
        return result.data;
      } else {
        setQuestionnaireDetail(null);
        return null;
      }
    } catch (error) {
      handleException(error, '获取问卷详情失败');
      setQuestionnaireDetail(null);
      return null;
    } finally {
      setDetailLoading(false);
    }
  }, []);

  // 获取学校课程列表
  const fetchSchoolCourses = useCallback(async (schoolCode: string) => {
    setCoursesLoading(true);
    try {
      const response = await getSchoolCourses(schoolCode);
      const result = handleApiResponse(response);

      if (result.success) {
        setSchoolCourses(result.data || []);
        return result.data;
      } else {
        setSchoolCourses([]);
        return [];
      }
    } catch (error) {
      handleException(error, '获取学校课程列表失败');
      setSchoolCourses([]);
      return [];
    } finally {
      setCoursesLoading(false);
    }
  }, []);

  // 获取问卷关联的课程列表
  const fetchQuestionnaireCourses = useCallback(
    async (questionnaireId: number) => {
      setCoursesLoading(true);
      try {
        const response = await getQuestionnaireCourses(questionnaireId);
        const result = handleApiResponse(response);

        if (result.success) {
          setQuestionnaireCourses(result.data || []);
          return result.data;
        } else {
          setQuestionnaireCourses([]);
          return [];
        }
      } catch (error) {
        handleException(error, '获取问卷关联课程失败');
        setQuestionnaireCourses([]);
        return [];
      } finally {
        setCoursesLoading(false);
      }
    },
    [],
  );

  return {
    // 状态
    questionnaireList,
    loading,
    total,
    questionnaireDetail,
    detailLoading,
    schoolCourses,
    questionnaireCourses,
    coursesLoading,

    // 方法
    fetchQuestionnaireList,
    fetchQuestionnaireDetail,
    createQuestionnaireAction,
    updateQuestionnaireAction,
    updateQuestionnaireStatusAction,
    deleteQuestionnaireAction,
    fetchSchoolCourses,
    fetchQuestionnaireCourses,
  };
}
