import type { IIncompleteStudentsResponse } from '@/types/statistics';
import { render } from '@testing-library/react';
import React from 'react';
import IncompleteStudents from './IncompleteStudents';

/**
 * IncompleteStudents 组件测试
 * 主要测试空数据和边界情况的处理
 */

describe('IncompleteStudents Component', () => {
  // 测试空数据情况
  test('renders empty state when data is null', () => {
    const { getByText } = render(
      <IncompleteStudents data={null} loading={false} />
    );
    expect(getByText('暂无未填写学生数据')).toBeInTheDocument();
  });

  // 测试空数组情况
  test('renders empty state when data arrays are empty', () => {
    const emptyData: IIncompleteStudentsResponse = {
      total_incomplete: 0,
      by_grade: [],
      by_class: [],
    };
    
    const { getByText } = render(
      <IncompleteStudents data={emptyData} loading={false} />
    );
    expect(getByText('0')).toBeInTheDocument(); // 应该显示0个未填写学生
  });

  // 测试undefined字段情况
  test('handles undefined fields gracefully', () => {
    const dataWithUndefined: IIncompleteStudentsResponse = {
      total_incomplete: 5,
      by_grade: [
        {
          grade_code: 'grade_1',
          grade_name: '', // 空字符串
          incomplete_count: 3,
          total_students: 30,
          completion_rate: 90,
        },
      ],
      by_class: [
        {
          grade_code: 'grade_1',
          grade_name: '',
          class_code: 'class_1',
          class_name: '',
          incomplete_count: 3,
          total_students: 30,
          completion_rate: 90,
          students: [],
        },
      ],
    };

    const { getByText } = render(
      <IncompleteStudents data={dataWithUndefined} loading={false} />
    );
    
    // 应该显示默认文本而不是空白
    expect(getByText('未知年级')).toBeInTheDocument();
    expect(getByText('未知班级')).toBeInTheDocument();
  });

  // 测试加载状态
  test('shows loading state', () => {
    const { container } = render(
      <IncompleteStudents data={null} loading={true} />
    );
    
    // 检查是否有loading相关的类名或元素
    expect(container.querySelector('.ant-spin')).toBeInTheDocument();
  });

  // 测试正常数据渲染
  test('renders normal data correctly', () => {
    const normalData: IIncompleteStudentsResponse = {
      total_incomplete: 5,
      by_grade: [
        {
          grade_code: 'grade_1',
          grade_name: '一年级',
          incomplete_count: 3,
          total_students: 30,
          completion_rate: 90,
        },
      ],
      by_class: [
        {
          grade_code: 'grade_1',
          grade_name: '一年级',
          class_code: 'class_1',
          class_name: '一班',
          incomplete_count: 3,
          total_students: 30,
          completion_rate: 90,
          students: [
            {
              sso_student_code: 'stu_001',
              sso_student_name: '张小明',
              grade_code: 'grade_1',
              grade_name: '一年级',
              class_code: 'class_1',
              class_name: '一班',
            },
          ],
        },
      ],
    };

    const { getByText } = render(
      <IncompleteStudents data={normalData} loading={false} />
    );
    
    expect(getByText('5')).toBeInTheDocument(); // 总未填写数
    expect(getByText('一年级')).toBeInTheDocument();
    expect(getByText('一班')).toBeInTheDocument();
    expect(getByText('张小明')).toBeInTheDocument();
  });
});
