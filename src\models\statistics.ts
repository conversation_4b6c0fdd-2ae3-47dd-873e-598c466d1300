import {
  getIncompleteStudents,
  getSchoolStatistics,
  getTeacherKeywords,
  getTeacherRanking,
  getTeacherScoreDistribution,
  getTeacherStatistics,
  getTrendAnalysis,
} from '@/services';
import type {
  IIncompleteStudentsQuery,
  IIncompleteStudentsResponse,
  IKeywordData,
  ISchoolStatistics,
  IScoreDistribution,
  IStatisticsQuery,
  ITeacherRanking,
  ITeacherStatistics,
} from '@/types/statistics';
import { handleApiResponse, handleException } from '@/utils/errorHandler';
import { useCallback, useState } from 'react';

/**
 * 统计分析数据模型
 */
export default function useStatisticsModel() {
  // 加载状态
  const [loading, setLoading] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);

  // 学校统计数据
  const [schoolStatistics, setSchoolStatistics] =
    useState<ISchoolStatistics | null>(null);

  // 教师排名数据
  const [teacherRanking, setTeacherRanking] = useState<ITeacherRanking[]>([]);
  const [teacherTotal, setTeacherTotal] = useState(0);

  // 教师详情数据
  const [teacherDetail, setTeacherDetail] = useState<ITeacherStatistics | null>(
    null,
  );
  const [scoreDistribution, setScoreDistribution] = useState<
    IScoreDistribution[]
  >([]);
  const [keywordData, setKeywordData] = useState<IKeywordData[]>([]);

  // 筛选条件
  const [filters, setFilters] = useState<IStatisticsQuery>({});

  // 未填写学生数据
  const [incompleteStudents, setIncompleteStudents] =
    useState<IIncompleteStudentsResponse | null>(null);
  const [incompleteStudentsLoading, setIncompleteStudentsLoading] =
    useState(false);

  // 获取学校统计数据
  const fetchSchoolStatistics = useCallback(
    async (params?: IStatisticsQuery) => {
      setLoading(true);
      try {
        const response = await getSchoolStatistics(params);
        const result = handleApiResponse(response);

        console.log('fetchSchoolStatistics', result);
        if (result.success) {
          setSchoolStatistics(result.data || null);
          return result.data;
        } else {
          setSchoolStatistics(null);
          return null;
        }
      } catch (error) {
        handleException(error, '获取学校统计数据失败');
        setSchoolStatistics(null);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取教师排名数据
  const fetchTeacherRanking = useCallback(async (params?: IStatisticsQuery) => {
    setLoading(true);
    try {
      const response = await getTeacherRanking(params);
      const result = handleApiResponse(response);

      if (result.success) {
        setTeacherRanking(result.data?.list || []);
        setTeacherTotal(result.data?.total || 0);
        return result.data;
      } else {
        setTeacherRanking([]);
        setTeacherTotal(0);
        return null;
      }
    } catch (error) {
      handleException(error, '获取教师排名失败');
      setTeacherRanking([]);
      setTeacherTotal(0);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取教师详情数据
  const fetchTeacherDetail = useCallback(
    async (teacherId: string, params?: IStatisticsQuery) => {
      setModalLoading(true);
      try {
        // 并行获取教师统计、评分分布、关键词数据
        const [statsResponse, distributionResponse, keywordsResponse] =
          await Promise.all([
            getTeacherStatistics({ ...params, sso_teacher_id: teacherId }),
            getTeacherScoreDistribution(teacherId, params),
            getTeacherKeywords(teacherId, params),
          ]);

        // 处理教师统计数据
        const statsResult = handleApiResponse(statsResponse);
        if (statsResult.success) {
          setTeacherDetail(statsResult.data || null);
        }

        // 处理评分分布数据
        const distributionResult = handleApiResponse(distributionResponse);
        if (distributionResult.success) {
          setScoreDistribution(distributionResult.data || []);
        }

        // 处理关键词数据
        const keywordsResult = handleApiResponse(keywordsResponse);
        if (keywordsResult.success) {
          setKeywordData(keywordsResult.data || []);
        }

        return {
          detail: statsResult.data,
          distribution: distributionResult.data,
          keywords: keywordsResult.data,
        };
      } catch (error) {
        handleException(error, '获取教师详情失败');
        setTeacherDetail(null);
        setScoreDistribution([]);
        setKeywordData([]);
        return null;
      } finally {
        setModalLoading(false);
      }
    },
    [],
  );



  // 获取未填写学生数据
  const fetchIncompleteStudents = useCallback(
    async (params: IIncompleteStudentsQuery) => {
      setIncompleteStudentsLoading(true);
      try {
        const response = await getIncompleteStudents(params);
        const result = handleApiResponse(response);

        if (result.success) {
          setIncompleteStudents(result.data || null);
          return result.data;
        } else {
          setIncompleteStudents(null);
          return null;
        }
      } catch (error) {
        handleException(error, '获取未填写学生数据失败');
        setIncompleteStudents(null);
        return null;
      } finally {
        setIncompleteStudentsLoading(false);
      }
    },
    [],
  );

  // 清空教师详情数据
  const clearTeacherDetail = useCallback(() => {
    setTeacherDetail(null);
    setScoreDistribution([]);
    setKeywordData([]);
  }, []);

  // 清空未填写学生数据
  const clearIncompleteStudents = useCallback(() => {
    setIncompleteStudents(null);
  }, []);

  return {
    // 状态
    loading,
    modalLoading,
    schoolStatistics,
    teacherRanking,
    teacherTotal,
    teacherDetail,
    scoreDistribution,
    keywordData,
    filters,
    incompleteStudents,
    incompleteStudentsLoading,

    // 方法
    fetchSchoolStatistics,
    fetchTeacherRanking,
    fetchTeacherDetail,
    fetchIncompleteStudents,
    clearTeacherDetail,
    clearIncompleteStudents,
  };
}
