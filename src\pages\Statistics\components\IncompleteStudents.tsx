import type {
  IIncompleteStudentsResponse,
  IIncompleteStudentsByClass,
  IIncompleteStudentsByGrade,
  IIncompleteStudentInfo,
} from '@/types/statistics';
import {
  ExclamationCircleOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import {
  Button,
  Card,
  Col,
  Collapse,
  Empty,
  Progress,
  Row,
  Space,
  Statistic,
  Tag,
  Tooltip,
} from 'antd';
import React, { useState } from 'react';

const { Panel } = Collapse;

interface IncompleteStudentsProps {
  data: IIncompleteStudentsResponse | null;
  loading?: boolean;
  onRefresh?: () => void;
}

/**
 * 未填写学生统计组件
 */
const IncompleteStudents: React.FC<IncompleteStudentsProps> = ({
  data,
  loading = false,
  onRefresh,
}) => {
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  if (!data || (!data.by_grade && !data.by_class)) {
    return (
      <Card title="未填写学生统计" loading={loading}>
        <Empty description="暂无未填写学生数据" />
      </Card>
    );
  }

  // 学生详情表格列定义
  const studentColumns: ProColumns<IIncompleteStudentInfo>[] = [
    {
      title: '学生姓名',
      dataIndex: 'sso_student_name',
      key: 'sso_student_name',
      width: 120,
      render: (_, record) => (
        <Space>
          <UserOutlined style={{ color: '#1890ff' }} />
          {record.sso_student_name}
        </Space>
      ),
    },
    {
      title: '学生代码',
      dataIndex: 'sso_student_code',
      key: 'sso_student_code',
      width: 120,
    },
    {
      title: '年级',
      dataIndex: 'grade_name',
      key: 'grade_name',
      width: 100,
      render: (_, record) => (
        <Tag color="blue">{record.grade_name}</Tag>
      ),
    },
    {
      title: '班级',
      dataIndex: 'class_name',
      key: 'class_name',
      width: 100,
      render: (_, record) => (
        <Tag color="green">{record.class_name}</Tag>
      ),
    },
  ];

  // 渲染年级统计卡片
  const renderGradeCard = (grade: IIncompleteStudentsByGrade) => (
    <Col span={6} key={grade.grade_code}>
      <Card size="small">
        <Statistic
          title={grade.grade_name || '未知年级'}
          value={grade.incomplete_count || 0}
          suffix={`/ ${grade.total_students || 0}`}
          prefix={<TeamOutlined style={{ color: '#fa8c16' }} />}
          valueStyle={{ color: '#fa8c16' }}
        />
        <Progress
          percent={grade.completion_rate || 0}
          size="small"
          status={(grade.completion_rate || 0) < 80 ? 'exception' : 'success'}
          format={(percent) => `${percent}%`}
        />
        <div style={{ fontSize: '12px', color: '#999', marginTop: 4 }}>
          完成率：{(grade.completion_rate || 0).toFixed(1)}%
        </div>
      </Card>
    </Col>
  );

  // 渲染班级详情
  const renderClassDetail = (classData: IIncompleteStudentsByClass) => (
    <div key={`${classData.grade_code}-${classData.class_code}`}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <Space>
          <Tag color="blue">{classData.grade_name || '未知年级'}</Tag>
          <Tag color="green">{classData.class_name || '未知班级'}</Tag>
          <span>
            未填写：{classData.incomplete_count || 0} / {classData.total_students || 0}
          </span>
        </Space>
        <Progress
          percent={classData.completion_rate || 0}
          size="small"
          style={{ width: 120 }}
          status={(classData.completion_rate || 0) < 80 ? 'exception' : 'success'}
        />
      </div>

      {classData.students && classData.students.length > 0 ? (
        <ProTable<IIncompleteStudentInfo>
          columns={studentColumns}
          dataSource={classData.students}
          rowKey="sso_student_code"
          search={false}
          options={false}
          pagination={false}
          size="small"
          scroll={{ x: 500 }}
        />
      ) : (
        <Empty description="该班级所有学生都已完成填写" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </div>
  );

  return (
    <Card
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: '#fa8c16' }} />
          未填写学生统计
        </Space>
      }
      extra={
        onRefresh && (
          <Button type="link" onClick={onRefresh} loading={loading}>
            刷新
          </Button>
        )
      }
      loading={loading}
    >
      {/* 总体统计 */}
      <div style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="未填写学生总数"
                value={data.total_incomplete || 0}
                suffix="人"
                prefix={<ExclamationCircleOutlined style={{ color: '#f5222d' }} />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
          <Col span={18}>
            <div style={{ padding: '16px 0' }}>
              <h4 style={{ marginBottom: 16 }}>各年级完成情况</h4>
              <Row gutter={16}>
                {data.by_grade?.map(renderGradeCard) || []}
              </Row>
            </div>
          </Col>
        </Row>
      </div>

      {/* 班级详情 */}
      {data.by_class && data.by_class.length > 0 && (
        <div>
          <h4 style={{ marginBottom: 16 }}>班级详情</h4>
          <Collapse
            activeKey={expandedKeys}
            onChange={(keys) => setExpandedKeys(keys as string[])}
          >
            {data.by_class.map((classData) => (
              <Panel
                header={
                  <Space>
                    <Tag color="blue">{classData.grade_name || '未知年级'}</Tag>
                    <Tag color="green">{classData.class_name || '未知班级'}</Tag>
                    <span>
                      未填写：{classData.incomplete_count || 0} / {classData.total_students || 0}
                    </span>
                    <Tooltip title={`完成率：${(classData.completion_rate || 0).toFixed(1)}%`}>
                      <Progress
                        percent={classData.completion_rate || 0}
                        size="small"
                        style={{ width: 100 }}
                        status={(classData.completion_rate || 0) < 80 ? 'exception' : 'success'}
                        showInfo={false}
                      />
                    </Tooltip>
                  </Space>
                }
                key={`${classData.grade_code}-${classData.class_code}`}
              >
                {renderClassDetail(classData)}
              </Panel>
            ))}
          </Collapse>
        </div>
      )}
    </Card>
  );
};

export default IncompleteStudents;
